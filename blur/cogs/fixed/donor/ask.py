import discord
import aiohttp
import os
from discord.ext import commands
from tools.ext import embed
from utils.parse import EmbedBuilder
from cogs.old.auth import owners


def is_donor():
    """Check if user is a donor or bot owner"""
    async def predicate(ctx: commands.Context):
        # Check if bot owner first
        if ctx.author.id in owners:
            return True
            
        # Check if donor
        try:
            async with ctx.bot.db.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT * FROM donor WHERE user_id = %s", (ctx.author.id,)
                    )
                    check = await cursor.fetchone()
            
            if not check:
                await embed.error(ctx, "This command is only available for **donors**")
                return False
            return True
        except Exception:
            await embed.error(ctx, "Database error occurred")
            return False
    return commands.check(predicate)


class AskAI(commands.Cog):
    """AI-powered embed assistance for donors"""

    def __init__(self, bot):
        self.bot = bot
        self.api_key = os.getenv('DEEP_API')
        self.api_url = "https://api.deepai.org/api/text-generator"

    async def call_deep_ai(self, prompt: str) -> str:
        """Call Deep AI API with the given prompt"""
        if not self.api_key:
            return "Deep AI API key not configured"
        
        headers = {
            'api-key': self.api_key,
            'Content-Type': 'application/json'
        }
        
        data = {
            'text': prompt
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.api_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('output', 'No response from AI')
                    else:
                        return f"API Error: {response.status}"
        except Exception as e:
            return f"Error calling AI: {str(e)}"

    def create_button_view(self, link_url: str) -> discord.ui.View:
        """Create a view with 3 buttons - 2 disabled grey ones and 1 link button in middle"""
        view = discord.ui.View()
        
        # First disabled button
        button1 = discord.ui.Button(
            style=discord.ButtonStyle.grey,
            emoji="🔒",
            disabled=True
        )
        
        # Middle link button
        button2 = discord.ui.Button(
            style=discord.ButtonStyle.link,
            emoji="🎤",
            label="voice",
            url=link_url
        )
        
        # Third disabled button
        button3 = discord.ui.Button(
            style=discord.ButtonStyle.grey,
            emoji="🔒",
            disabled=True
        )
        
        view.add_item(button1)
        view.add_item(button2)
        view.add_item(button3)
        
        return view

    @commands.command(name="ask", description="Ask AI to help with embed creation or modification")
    @is_donor()
    async def ask_ai(self, ctx: commands.Context, *, prompt: str):
        """Ask AI to help with embed creation or modification"""
        try:
            # Show typing indicator
            async with ctx.typing():
                # Check if user provided an embed code to modify
                if any(keyword in prompt.lower() for keyword in ['fix', 'modify', 'change', 'update', 'improve']) and '{' in prompt:
                    # Look for embed code in the prompt
                    embed_start = prompt.find('{')
                    embed_end = prompt.rfind('}') + 1
                    embed_code = prompt[embed_start:embed_end]
                    instruction = prompt[:embed_start].strip() + " " + prompt[embed_end:].strip()

                    ai_prompt = f"""You are an expert Discord embed creator. The user wants to {instruction.strip()}.

Current embed code: {embed_code}

Please provide an improved embed code using this exact format:
{{content: message content}}$v{{description: embed description}}$v{{color: #hexcolor}}$v{{thumbnail: image_url}}$v{{button: label|url|style}}

Rules:
- Use $v to separate different parts
- For buttons: label|url|link for link buttons, label||primary for colored buttons
- Colors should be hex codes like #1e1f22
- Support placeholders like {{user.mention}}, {{user.avatar}}, {{server.name}}
- Keep descriptions concise and well-formatted
- Use > for quote formatting in descriptions
- For 3 buttons with middle link: {{button: 🔒||disabled}}$v{{button: voice|https://discord.com/channels/1384275785325608970/1391022408986529843|link}}$v{{button: 🔒||disabled}}

Provide ONLY the embed code, no explanations."""

                elif 'button' in prompt.lower() and 'voice' in prompt.lower():
                    # Special handling for the voice button request
                    ai_prompt = f"""You are an expert Discord embed creator. The user wants: {prompt}

Based on the request, create an embed code with 3 buttons (2 disabled grey ones and 1 voice link in middle).

Please create an embed code using this exact format:
{{content: wlc in /paura {{user.mention}}}}$v{{description: > checkout our [shop](https://discord.com/channels/1384275785325608970/1389927880145768518)
> be active in [txt](https://discord.com/channels/1384275785325608970/1389453066771628164) nd check [cmds](https://discord.com/channels/1384275785325608970/1386471033242128488)
> make sure to claim your [roles](https://discord.com/channels/1384275785325608970/1388577848134074379)
> rep `/paura` in ur status for perks}}$v{{color: #1e1f22}}$v{{thumbnail: {{user.avatar}}}}$v{{button: 🔒||disabled}}$v{{button: voice|https://discord.com/channels/1384275785325608970/1391022408986529843|link}}$v{{button: 🔒||disabled}}

Provide ONLY the embed code, no explanations."""

                else:
                    # General embed creation request
                    ai_prompt = f"""You are an expert Discord embed creator. Create an embed based on this request: {prompt}

Please create an embed code using this exact format:
{{content: message content}}$v{{description: embed description}}$v{{color: #hexcolor}}$v{{thumbnail: image_url}}$v{{button: label|url|style}}

Rules:
- Use $v to separate different parts
- For buttons: label|url|link for link buttons, label||primary for colored buttons, emoji||disabled for disabled buttons
- Colors should be hex codes like #1e1f22
- Support placeholders like {{user.mention}}, {{user.avatar}}, {{server.name}}
- Keep descriptions concise and well-formatted
- Use > for quote formatting in descriptions

Provide ONLY the embed code, no explanations."""

                # Get AI response
                ai_response = await self.call_deep_ai(ai_prompt)
                
                # Clean up the response to extract just the embed code
                embed_code = ai_response.strip()
                
                # Remove any explanatory text and keep only the embed code
                lines = embed_code.split('\n')
                for line in lines:
                    if '{' in line and ('content:' in line or 'description:' in line or 'title:' in line):
                        embed_code = line.strip()
                        break
                
                # If the user's example included the voice link, add it
                if 'voice' in prompt.lower() and 'https://discord.com/channels/1384275785325608970/1391022408986529843' in prompt:
                    if 'button:' not in embed_code:
                        embed_code += "$v{button: voice|https://discord.com/channels/1384275785325608970/1391022408986529843|link}"
                
                # Try to parse and send the embed
                try:
                    builder = EmbedBuilder(ctx)
                    result = builder.parse_embed_string(embed_code)
                    
                    embed_obj = result.get('embed')
                    content = result.get('content')
                    view = result.get('view')
                    
                    # If no view was created but we need the voice button, create it
                    if not view and 'voice' in prompt.lower():
                        view = self.create_button_view("https://discord.com/channels/1384275785325608970/1391022408986529843")
                    
                    # Send the result
                    if embed_obj or content:
                        await ctx.send(
                            content=content,
                            embed=embed_obj,
                            view=view
                        )
                        
                        # Also send the code for reference
                        code_embed = discord.Embed(
                            title="🤖 AI Generated Embed Code",
                            description=f"```{embed_code}```",
                            color=0x00ff00
                        )
                        await ctx.send(embed=code_embed)
                    else:
                        await embed.error(ctx, "AI couldn't generate a valid embed")
                        
                except Exception as parse_error:
                    # If parsing fails, just show the raw AI response
                    await embed.error(ctx, f"Error parsing AI response: {parse_error}")
                    
                    # Send raw response for debugging
                    debug_embed = discord.Embed(
                        title="🤖 Raw AI Response",
                        description=f"```{ai_response[:1900]}```",
                        color=0xff9900
                    )
                    await ctx.send(embed=debug_embed)
                    
        except Exception as e:
            await embed.error(ctx, f"Error processing AI request: {str(e)}")


async def setup(bot):
    await bot.add_cog(AskAI(bot))
